import { User } from "./user";
import { ParkingSpot } from "./parking-spot";
import { Reservation } from "./reservation";
import { Rental } from "./rental";
import { Infraction } from "./infraction";
import { Payment } from "./payment";
import { Visit } from "./visit";
import { Pet } from "./pet";
import { Vehicle } from "./vehicle";
import { Tag } from "./tag";
import { Fine } from "./fine";
import { Complaint } from "./complaint";
import { MonthlyMaintenanceCharge } from "./monthly-maintenance-charge";
import { MaintenanceIssueReport } from "./maintenance-issue-report";
import { Facility } from "./facility";
import { Package } from "./package";
import { ComplaintType } from "./complaint-type";

export interface PropertyGenerals {
  id: string;
  address: string;
  type: PropertyType;
}

export interface Property {
  id: string;
  address: string;
  type: PropertyType;
  createdAt: string;
  updatedAt: string;
  residents: User[];
  reservations: Reservation[];
  rentals: Rental[];
  infractions: Infraction[];
  payments: Payment[];
  visits: Visit[];
  parkingSpots: ParkingSpot[];
  pets: Pet[];
  vehicles: Vehicle[];
  tags: Tag[];
  fines: Fine[];
  complaints: Complaint[];
  maintenanceIssueReports: MaintenanceIssueReport[];
  monthlyMaintenanceCharges: MonthlyMaintenanceCharge[];
  packages: Package[];
}

export enum PropertyType {
  HOUSE = "HOUSE",
  DEPARTMENT = "DEPARTMENT",
}

export interface PartialProperty {
  id: Property["id"];
  address: Property["address"];
  type: Property["type"];
  _count: {
    parkingSpots: number;
    pets: number;
    residents: number;
    tags: number;
    vehicles: number;
  };
  reservations: PropertyReservation[];
  maintenanceIssueReports: PropertyMaintenanceIssueReport[];
  infractions: Infraction[];
  complaints: PropertyComplaint[];
  fines: PropertyFine[];
  packages: PropertyPackage[];
}

////////////////////

export interface PropertyPackage {
  id: Package["id"];
  number: Package["number"];
  status: Package["status"];
  receivedAt: Package["receivedAt"];
  deliveredAt: Package["deliveredAt"];
  notes: Package["notes"];
}

export interface PropertyReservation {
  id: Reservation["id"];
  status: Reservation["status"];
  startDateTime: Reservation["startDateTime"];
  endDateTime: Reservation["endDateTime"];
  amountOfPeople: Reservation["amountOfPeople"];
  createdAt: Reservation["createdAt"];
  facility: { name: Facility["name"] };
}

export interface PropertyFine {
  id: Fine["id"];
  amount: Fine["amount"];
  paidAt: Fine["paidAt"];
  description: Fine["description"];
  issuedAt: Fine["issuedAt"];
  isPaid: Fine["isPaid"];
}

export interface PropertyInfraction {
  id: Infraction["id"];
  description: Infraction["description"];
  date: Infraction["date"];
  severity: Infraction["severity"];
}

export interface PropertyComplaint {
  id: Complaint["id"];
  detail: Complaint["detail"];
  priority: Complaint["priority"];
  status: Complaint["status"];
  completedAt: Complaint["completedAt"];
  createdAt: Complaint["createdAt"];
  complaintType: {
    name: ComplaintType["name"];
    description: ComplaintType["description"];
  };
}

export interface PropertyMaintenanceIssueReport {
  id: MaintenanceIssueReport["id"];
  description: MaintenanceIssueReport["description"];
  status: MaintenanceIssueReport["status"];
  createdAt: MaintenanceIssueReport["createdAt"];
}
