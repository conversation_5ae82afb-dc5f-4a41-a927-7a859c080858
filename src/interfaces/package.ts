import { Property } from "./property";
import { User } from "./user";

export enum PackageStatus {
  PENDING = "PENDING",
  DELIVERED = "DELIVERED",
  EXPIRED = "EXPIRED",
}

export interface Package {
  id: string;
  number: number;
  status: PackageStatus;
  deliveryToken?: string;
  tokenExpiresAt?: string;
  receivedAt: string;
  deliveredAt?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  propertyId: Property["id"];
  receivedByUser: UserPackage;
  deliveredToUser?: UserPackage;
  delivertedByUser?: UserPackage;
  images: PackageImage[];
}

export interface UserPackage {
  id: User["id"];
  firstName: User["firstName"];
  paternalLastName: User["paternalLastName"];
  maternalLastName: User["maternalLastName"];
}

export interface PackageImage {
  id: string;
  path: string;
  packageId: string;
  createdAt: Date;
}

export interface PackageDeliveryToken {
  token: string;
  expiresAt: string;
}
