import { ComplaintType } from "./complaint-type";
import { Status } from "./maintenance-issue-report";
import { Property } from "./property";
import { User } from "./user";

export interface Complaint {
  id: string;
  propertyId: Property["id"];
  userId: User["id"];
  complaintTypeId: ComplaintType["id"];

  detail: string;
  status: Status;
  priority: Priority;
  completedAt: string;
  createdAt: string;
  complaintType: ComplaintType;
  images: ComplaintImage[];
}

export enum Priority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
}

export interface CreateComplaint {
  propertyId: string;
  complaintTypeId: string;
  detail: string;
  priority: Priority;
  images?: string[];
}

export interface ComplaintImage {
  id: string;
  path: string;
  complaintId: string;
  createdAt: Date;
}
