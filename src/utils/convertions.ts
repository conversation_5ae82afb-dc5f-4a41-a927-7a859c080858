import { Priority } from "../interfaces/complaint";
import { Status } from "../interfaces/maintenance-issue-report";
import { InfractionSeverity } from "../interfaces/infraction";
import { ReservationStatus } from "../interfaces/reservation";
import { theme } from "../theme";
import type { MaterialCommunityIcons } from "@expo/vector-icons";
import { PropertyType } from "../interfaces/property";
import { PackageStatus } from "../interfaces/package";

interface InfractionStatusData {
  status: "Menor" | "Moderada" | "Grave";
  backgroundColor: string;
  borderColor: string;
}

export const getProirity = (priority: Priority) => {
  if (priority === Priority.HIGH) return "Alta";
  if (priority === Priority.MEDIUM) return "Media";
  if (priority === Priority.LOW) return "Baja";
  return "Sin prioridad";
};

export const getStatus = (status: Status) => {
  if (status === Status.RESOLVED) return "Resuelta";
  if (status === Status.OPEN) return "Abierta";
  if (status === Status.IN_PROGRESS) return "En progreso";
};

export const getInfractionStatus = (
  status: InfractionSeverity
): InfractionStatusData => {
  if (status === InfractionSeverity.MINOR)
    return {
      status: "Menor",
      backgroundColor: theme.colors.yellow.light,
      borderColor: theme.colors.yellow.dark,
    };
  if (status === InfractionSeverity.MODERATE)
    return {
      status: "Moderada",
      backgroundColor: theme.colors.orange.light,
      borderColor: theme.colors.orange.dark,
    };
  if (status === InfractionSeverity.SEVERE)
    return {
      status: "Grave",
      backgroundColor: theme.colors.red.light,
      borderColor: theme.colors.red.dark,
    };
  return {
    status: "Menor",
    backgroundColor: theme.colors.error,
    borderColor: theme.colors.error,
  };
};

export const getReservationStatus = (
  reservationStatus: ReservationStatus
): string => {
  if (reservationStatus === ReservationStatus.APPROVED) return "Aprobado";
  if (reservationStatus === ReservationStatus.PENDING) return "Pendiente";
  if (reservationStatus === ReservationStatus.REJECTED) return "Rechazado";
  return "";
};

export const getPriorityColor = (priority: Priority): string => {
  switch (priority) {
    case Priority.HIGH:
      return theme.colors.error;
    case Priority.MEDIUM:
      return theme.colors.warning;
    case Priority.LOW:
      return theme.colors.success;
    default:
      return theme.colors.gray500;
  }
};

export const getPriorityIcon = (
  priority: Priority
): keyof typeof MaterialCommunityIcons.glyphMap => {
  switch (priority) {
    case Priority.HIGH:
      return "alert-circle";
    case Priority.MEDIUM:
      return "alert";
    case Priority.LOW:
      return "information-outline";
    default:
      return "help-circle";
  }
};

export const getPriorityLabel = (priority: Priority): string => {
  switch (priority) {
    case Priority.HIGH:
      return "Alta";
    case Priority.MEDIUM:
      return "Media";
    case Priority.LOW:
      return "Baja";
    default:
      return "Sin prioridad";
  }
};

export const getStatusColor = (status: Status): string => {
  switch (status) {
    case Status.OPEN:
      return theme.colors.primary;
    case Status.IN_PROGRESS:
      return theme.colors.warning;
    case Status.RESOLVED:
      return theme.colors.success;
    default:
      return theme.colors.gray500;
  }
};

export const getStatusIcon = (
  status: Status
): keyof typeof MaterialCommunityIcons.glyphMap => {
  switch (status) {
    case Status.OPEN:
      return "clock-outline";
    case Status.IN_PROGRESS:
      return "clock-fast";
    case Status.RESOLVED:
      return "check-circle";
    default:
      return "help-circle";
  }
};

export const getStatusLabel = (status: Status): string => {
  switch (status) {
    case Status.OPEN:
      return "Abierta";
    case Status.IN_PROGRESS:
      return "En Progreso";
    case Status.RESOLVED:
      return "Resuelta";
    default:
      return "Sin estado";
  }
};

export const getChargeStatus = (
  isPaid: boolean,
  isOverdue: boolean
): {
  color: string;
  label: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
} => {
  if (isPaid) {
    return {
      color: theme.colors.success,
      label: "Pagado",
      icon: "check-circle",
    };
  } else if (isOverdue) {
    return {
      color: theme.colors.error,
      label: "Vencido",
      icon: "alert-circle",
    };
  } else {
    return {
      color: theme.colors.warning,
      label: "Pendiente",
      icon: "clock-outline",
    };
  }
};

export const getSeverityData = (
  severity: InfractionSeverity
): {
  color: string;
  label: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
} => {
  if (severity === InfractionSeverity.SEVERE)
    return {
      color: theme.colors.error,
      label: "Grave",
      icon: "alert-octagon",
    };
  if (severity === InfractionSeverity.MODERATE)
    return {
      color: theme.colors.warning,
      label: "Moderada",
      icon: "alert",
    };
  if (severity === InfractionSeverity.MINOR)
    return {
      color: theme.colors.success,
      label: "Menor",
      icon: "information",
    };
  return {
    color: theme.colors.gray500,
    label: "Sin clasificar",
    icon: "help-circle",
  };
};

export const getReservationStatusData = (
  status: ReservationStatus
): {
  color: string;
  label: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
} => {
  if (status === ReservationStatus.APPROVED)
    return {
      color: theme.colors.success,
      label: "Aprobada",
      icon: "check-circle",
    };
  if (status === ReservationStatus.PENDING)
    return {
      color: theme.colors.warning,
      label: "Pendiente",
      icon: "clock-outline",
    };
  if (status === ReservationStatus.REJECTED)
    return {
      color: theme.colors.error,
      label: "Rechazada",
      icon: "close-circle",
    };
  return {
    color: theme.colors.gray500,
    label: "Sin estado",
    icon: "help-circle",
  };
};

export const getPetIcon = (
  type: string
): keyof typeof MaterialCommunityIcons.glyphMap => {
  const petType = type.toLowerCase();
  if (petType.includes("perro") || petType.includes("dog")) {
    return "dog";
  } else if (petType.includes("gato") || petType.includes("cat")) {
    return "cat";
  } else if (
    petType.includes("ave") ||
    petType.includes("bird") ||
    petType.includes("pájaro")
  ) {
    return "bird";
  } else if (petType.includes("pez") || petType.includes("fish")) {
    return "fish";
  } else if (petType.includes("conejo") || petType.includes("rabbit")) {
    return "rabbit";
  } else {
    return "paw";
  }
};

export const getRoleData = (
  roleName: string
): {
  color: string;
  label: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
} => {
  const role = roleName.toLowerCase();
  if (role.includes("propietario") || role.includes("owner")) {
    return {
      color: theme.colors.gold,
      label: "Propietario",
      icon: "crown",
    };
  } else if (role.includes("inquilino") || role.includes("tenant")) {
    return {
      color: theme.colors.secondary,
      label: "Inquilino",
      icon: "home-account",
    };
  } else if (role.includes("administrador") || role.includes("admin")) {
    return {
      color: theme.colors.primary,
      label: "Administrador",
      icon: "shield-account",
    };
  } else if (role.includes("conserje") || role.includes("portero")) {
    return {
      color: theme.colors.secondary,
      label: "Conserje",
      icon: "account-tie",
    };
  } else {
    return {
      color: theme.colors.gray500,
      label: "Sin rol",
      icon: "account",
    };
  }
};

export const getPropertyTypeLabel = (type: PropertyType): string => {
  switch (type) {
    case PropertyType.HOUSE:
      return "Casa";
    case PropertyType.DEPARTMENT:
      return "Departamento";
    default:
      return "Propiedad";
  }
};

export const getPropertyIcon = (
  type: PropertyType
): keyof typeof MaterialCommunityIcons.glyphMap => {
  switch (type) {
    case PropertyType.HOUSE:
      return "home";
    case PropertyType.DEPARTMENT:
      return "office-building";
    default:
      return "map-marker";
  }
};

export const getPetColor = (type: string): string => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes("perro") || lowerType.includes("dog"))
    return theme.colors.warning;
  if (lowerType.includes("gato") || lowerType.includes("cat"))
    return theme.colors.primary;
  if (
    lowerType.includes("ave") ||
    lowerType.includes("bird") ||
    lowerType.includes("pájaro")
  )
    return theme.colors.secondary;
  if (lowerType.includes("pez") || lowerType.includes("fish"))
    return theme.colors.success;
  if (lowerType.includes("conejo") || lowerType.includes("rabbit"))
    return theme.colors.gray500;
  return theme.colors.primary;
};

export const getCategoryIcon = (
  description?: string
): keyof typeof MaterialCommunityIcons.glyphMap => {
  if (!description) return "wrench";

  const desc = description.toLowerCase();
  if (
    desc.includes("plomería") ||
    desc.includes("agua") ||
    desc.includes("tubería")
  ) {
    return "pipe";
  } else if (
    desc.includes("eléctrico") ||
    desc.includes("luz") ||
    desc.includes("electricidad")
  ) {
    return "lightning-bolt";
  } else if (
    desc.includes("pintura") ||
    desc.includes("pared") ||
    desc.includes("pintar")
  ) {
    return "format-paint";
  } else if (
    desc.includes("jardín") ||
    desc.includes("jardinería") ||
    desc.includes("plantas")
  ) {
    return "flower";
  } else if (desc.includes("limpieza") || desc.includes("limpiar")) {
    return "broom";
  } else if (
    desc.includes("seguridad") ||
    desc.includes("puerta") ||
    desc.includes("cerradura")
  ) {
    return "shield";
  } else if (
    desc.includes("aire") ||
    desc.includes("ventilación") ||
    desc.includes("clima")
  ) {
    return "air-conditioner";
  } else {
    return "wrench";
  }
};

export const getCategoryColor = (description?: string): string => {
  if (!description) return theme.colors.secondary;

  const desc = description.toLowerCase();
  if (desc.includes("plomería") || desc.includes("agua")) {
    return theme.colors.primary;
  } else if (desc.includes("eléctrico") || desc.includes("luz")) {
    return theme.colors.warning;
  } else if (desc.includes("pintura") || desc.includes("pared")) {
    return theme.colors.success;
  } else if (desc.includes("jardín") || desc.includes("plantas")) {
    return theme.colors.success;
  } else if (desc.includes("seguridad") || desc.includes("puerta")) {
    return theme.colors.error;
  } else {
    return theme.colors.secondary;
  }
};

export const getPackageStatusData = (
  status: PackageStatus
): {
  color: string;
  label: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
} => {
  if (status === PackageStatus.PENDING)
    return {
      color: theme.colors.warning,
      label: "Pendiente",
      icon: "clock-outline",
    };
  if (status === PackageStatus.DELIVERED)
    return {
      color: theme.colors.success,
      label: "Entregado",
      icon: "check-circle",
    };
  if (status === PackageStatus.EXPIRED)
    return {
      color: theme.colors.error,
      label: "Expirado",
      icon: "close-circle",
    };
  return {
    color: theme.colors.gray500,
    label: "Sin estado",
    icon: "package-variant",
  };
};
