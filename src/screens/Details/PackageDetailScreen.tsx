import { StyleSheet, Text, View, ScrollView, Alert, Image } from "react-native";
import { useState } from "react";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { theme } from "../../theme";
import { PackageDetailRouteProp } from "../../navigation/types";
import { formatDateTime } from "../../utils/date-time.utils";
import { DetailTopCard } from "../../components/main/cards/DetailTopCard";
import { DetailCardBase } from "../../components/main/cards/DetailCardBase";
import { Package, PackageStatus } from "../../interfaces/package";
import { usePackages } from "../../hooks/usePackages";
import { Button } from "../../components";
import { LoadingOverlay } from "../../components/LoadingOverlay";
import { SuccessModal } from "../../components/modals/SuccessModal";
import { ErrorModal } from "../../components/modals/ErrorModal";
import { getPackageStatusData } from "../../utils/convertions";
import { Loading } from "../../components/Loading";
import { useCachedQuery } from "../../hooks/useCachedQueries";

export const PackageDetailScreen: React.FC = () => {
  const route = useRoute<PackageDetailRouteProp>();
  const { packageId } = route.params;

  const {
    data: pkgData,
    isLoading,
    refetch,
  } = useCachedQuery<Package>(`mobile/package/${packageId}`);

  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [generatedToken, setGeneratedToken] = useState<string>("");

  const { generateDeliveryToken } = usePackages();

  if (isLoading) return <Loading />;
  if (!pkgData) return <Text>Sin datos</Text>;

  const pkg = pkgData;

  const statusData = getPackageStatusData(pkg.status);

  const handleGenerateToken = () => {
    Alert.alert(
      "Generar Token de Entrega",
      "¿Estás seguro de que quieres generar un token de entrega? Este token será válido por 24 horas.",
      [
        {
          text: "Cancelar",
          style: "cancel",
        },
        {
          text: "Generar",
          onPress: () => {
            generateDeliveryToken.mutate(pkg.id, {
              onSuccess: (data) => {
                setGeneratedToken(data.token);
                setShowSuccessModal(true);
                refetch();
              },
              onError: (error: any) => {
                setErrorMessage(
                  error.response?.data?.message ?? "Error al generar el token"
                );
                setShowErrorModal(true);
              },
            });
          },
        },
      ]
    );
  };

  const canGenerateToken =
    pkg.status === PackageStatus.PENDING && !pkg.deliveryToken;

  return (
    <GradientView firstLineText="Detalle de Paquete">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Card */}
        <DetailTopCard
          iconItem={{
            icon: "package-variant",
            color: theme.colors.primary,
          }}
          label="Paquete"
          tags={[{ label: statusData.label, color: statusData.color }]}
        />

        {/* Package Details */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información del Paquete</Text>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="account-arrow-down"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Recibido por</Text>
              <Text style={styles.detailValue}>
                {pkg.receivedByUser.firstName}{" "}
                {pkg.receivedByUser.paternalLastName}{" "}
                {pkg.receivedByUser.maternalLastName}
              </Text>
            </View>
          </View>

          {pkg.deliveredToUser && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons
                name="account-check"
                size={20}
                color={theme.colors.primary}
              />
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Entregado a</Text>
                <Text style={styles.detailValue}>
                  {pkg.deliveredToUser.firstName}{" "}
                  {pkg.deliveredToUser.paternalLastName}{" "}
                  {pkg.deliveredToUser.maternalLastName}
                </Text>
              </View>
            </View>
          )}

          {pkg.delivertedByUser && pkg.status === PackageStatus.DELIVERED && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons
                name="account-arrow-up"
                size={20}
                color={theme.colors.primary}
              />
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Entregado por</Text>
                <Text style={styles.detailValue}>
                  {pkg.delivertedByUser.firstName}{" "}
                  {pkg.delivertedByUser.paternalLastName}{" "}
                  {pkg.delivertedByUser.maternalLastName}
                </Text>
              </View>
            </View>
          )}

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="calendar"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de Recepción</Text>
              <Text style={styles.detailValue}>
                {formatDateTime(pkg.receivedAt)}
              </Text>
            </View>
          </View>

          {pkg.deliveredAt && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color={theme.colors.success}
              />
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Fecha de Entrega</Text>
                <Text style={styles.detailValue}>
                  {formatDateTime(pkg.deliveredAt)}
                </Text>
              </View>
            </View>
          )}

          {pkg.notes && (
            <View style={styles.detailRow}>
              <MaterialCommunityIcons
                name="note-text"
                size={20}
                color={theme.colors.primary}
              />
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Notas</Text>
                <Text style={styles.detailValue}>{pkg.notes}</Text>
              </View>
            </View>
          )}
        </DetailCardBase>

        {/* Token Information */}
        {pkg.deliveryToken && (
          <DetailCardBase border>
            <Text style={styles.sectionTitle}>Token de Entrega</Text>

            <View style={styles.tokenContainer}>
              <Text style={styles.tokenLabel}>Token:</Text>
              <Text style={styles.tokenValue}>{pkg.deliveryToken}</Text>
            </View>

            {pkg.tokenExpiresAt && (
              <View style={styles.detailRow}>
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={20}
                  color={theme.colors.warning}
                />
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Expira el</Text>
                  <Text style={styles.detailValue}>
                    {formatDateTime(pkg.tokenExpiresAt)}
                  </Text>
                </View>
              </View>
            )}
          </DetailCardBase>
        )}

        {/* Images */}
        {pkg.images && pkg.images.length > 0 && (
          <DetailCardBase border>
            <Text style={styles.sectionTitle}>Imágenes del Paquete</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.imagesContainer}
            >
              {pkg.images.map((image, index) => (
                <View key={image.id ?? index} style={styles.imageWrapper}>
                  <Image
                    source={{ uri: image.path }}
                    style={styles.packageImage}
                    resizeMode="cover"
                  />
                </View>
              ))}
            </ScrollView>
          </DetailCardBase>
        )}

        {/* Generate Token Button */}
        {canGenerateToken && (
          <View style={styles.buttonContainer}>
            <Button
              title="Generar Token de Entrega"
              onPress={handleGenerateToken}
              disabled={generateDeliveryToken.isPending}
            />
          </View>
        )}
      </ScrollView>

      {/* Loading Overlay */}
      {generateDeliveryToken.isPending && <LoadingOverlay visible={true} />}

      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title="Token Generado"
        message={`Token de entrega generado exitosamente: ${generatedToken}`}
        onClose={() => setShowSuccessModal(false)}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={showErrorModal}
        title="Error"
        message={errorMessage}
        onClose={() => setShowErrorModal(false)}
      />
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.md,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  detailContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.gray500,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: theme.colors.black,
    fontWeight: "500",
  },
  tokenContainer: {
    backgroundColor: theme.colors.primary + "10",
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    marginBottom: theme.spacing.md,
  },
  tokenLabel: {
    fontSize: 14,
    color: theme.colors.gray500,
    marginBottom: 4,
  },
  tokenValue: {
    fontSize: 18,
    color: theme.colors.primary,
    fontWeight: "600",
    fontFamily: "monospace",
  },
  buttonContainer: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  generateButton: {
    backgroundColor: theme.colors.primary,
  },
  imagesContainer: {
    marginTop: theme.spacing.sm,
  },
  imageWrapper: {
    marginRight: theme.spacing.md,
  },
  packageImage: {
    width: 120,
    height: 120,
    borderRadius: theme.radii.md,
    backgroundColor: theme.colors.gray100,
  },
});
