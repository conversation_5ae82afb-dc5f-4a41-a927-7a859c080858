import { StyleSheet, Text, View, ScrollView, Image } from "react-native";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/cards/Card";
import { theme } from "../../theme";
import { MaintenanceIssueReportDetailRouteProp } from "../../navigation/types";
import { formatDateDMY } from "../../utils/date-time.utils";
import {
  getStatusColor,
  getStatusIcon,
  getStatusLabel,
} from "../../utils/convertions";
import { DetailCardBase } from "../../components/main/cards/DetailCardBase";
import { DetailTopCard } from "../../components/main/cards/DetailTopCard";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { MaintenanceIssueReport } from "../../interfaces/maintenance-issue-report";
import { Loading } from "../../components/Loading";

export const MaintenanceIssueReportDetailScreen: React.FC = () => {
  const route = useRoute<MaintenanceIssueReportDetailRouteProp>();
  const { maintenanceIssueReportId } = route.params;

  const {
    data: report,
    isLoading,
    error,
  } = useCachedQuery<MaintenanceIssueReport>(
    `mobile/maintenance-issue-report/${maintenanceIssueReportId}`
  );

  if (isLoading) return <Loading />;
  if (error) return <Text>Error</Text>;
  if (!report) return <Text>Sin datos</Text>;

  const statusColor = getStatusColor(report.status);
  const statusIcon = getStatusIcon(report.status);
  const statusLabel = getStatusLabel(report.status);

  return (
    <GradientView firstLineText="Detalle de Reporte">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Card */}
        <DetailTopCard
          iconItem={{
            icon: "tools",
            color: theme.colors.primary,
          }}
          label="Reporte"
          tags={[{ label: statusLabel, color: statusColor }]}
        />

        {/* Report Details */}
        <DetailCardBase border>
          <Text style={styles.sectionTitle}>Información del Reporte</Text>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="text-box"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Descripción</Text>
              <Text style={styles.detailValue}>{report.description}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name={statusIcon}
              size={20}
              color={statusColor}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Estado</Text>
              <Text style={[styles.detailValue, { color: statusColor }]}>
                {statusLabel}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="calendar-plus"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Fecha de creación</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(report.createdAt)}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="calendar-edit"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Última actualización</Text>
              <Text style={styles.detailValue}>
                {formatDateDMY(report.updatedAt)}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <MaterialCommunityIcons
              name="identifier"
              size={20}
              color={theme.colors.primary}
            />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>ID de Reporte</Text>
              <Text style={styles.detailValue}>{report.id}</Text>
            </View>
          </View>
        </DetailCardBase>

        {/* Images */}
        {"images" in report && report.images && report.images.length > 0 && (
          <Card style={styles.detailCard}>
            <Text style={styles.sectionTitle}>Imágenes del Reporte</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.imagesContainer}
            >
              {report.images.map((image, index) => (
                <View key={image.id ?? index} style={styles.imageWrapper}>
                  <Image
                    source={{ uri: image.path }}
                    style={styles.reportImage}
                    resizeMode="cover"
                  />
                </View>
              ))}
            </ScrollView>
          </Card>
        )}

        {/* Status Information */}
        {report.status === "OPEN" && (
          <Card style={styles.infoCard}>
            <View style={styles.infoContainer}>
              <MaterialCommunityIcons
                name="information"
                size={20}
                color={theme.colors.primary}
              />
              <Text style={styles.infoText}>
                Su reporte ha sido recibido y está pendiente de revisión.
                Recibirá actualizaciones sobre el progreso.
              </Text>
            </View>
          </Card>
        )}

        {report.status === "IN_PROGRESS" && (
          <Card style={styles.warningCard}>
            <View style={styles.warningContainer}>
              <MaterialCommunityIcons
                name="clock-fast"
                size={20}
                color={theme.colors.warning}
              />
              <Text style={styles.warningText}>
                Su reporte está siendo atendido. El equipo de mantenimiento está
                trabajando en la solución.
              </Text>
            </View>
          </Card>
        )}

        {report.status === "RESOLVED" && (
          <Card style={styles.successCard}>
            <View style={styles.successContainer}>
              <MaterialCommunityIcons
                name="check-circle"
                size={24}
                color={theme.colors.success}
              />
              <View style={styles.successContent}>
                <Text style={styles.successTitle}>Reporte Resuelto</Text>
                <Text style={styles.successText}>
                  Su reporte de mantenimiento ha sido resuelto exitosamente. Si
                  el problema persiste, no dude en crear un nuevo reporte.
                </Text>
              </View>
            </View>
          </Card>
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  headerRow: {
    marginBottom: 0,
  },
  statusIndicator: {
    width: 64,
    height: 64,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  reportTitle: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  statusChip: {
    alignSelf: "flex-start",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
  },
  statusText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  detailCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    flexDirection: "column",
  },
  sectionTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.lg,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
  },
  detailContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  detailLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    lineHeight: 20,
  },
  imagesContainer: {
    marginTop: theme.spacing.sm,
  },
  imageWrapper: {
    marginRight: theme.spacing.md,
  },
  reportImage: {
    width: 120,
    height: 120,
    borderRadius: theme.radii.md,
    backgroundColor: theme.colors.gray100,
  },
  infoCard: {
    marginBottom: theme.spacing.md,
    backgroundColor: `${theme.colors.primary}05`,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  infoContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: theme.spacing.md,
  },
  infoText: {
    flex: 1,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
    lineHeight: 20,
  },
  warningCard: {
    marginBottom: theme.spacing.md,
    backgroundColor: `${theme.colors.warning}05`,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.warning,
  },
  warningContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: theme.spacing.md,
  },
  warningText: {
    flex: 1,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.warning,
    marginLeft: theme.spacing.sm,
    lineHeight: 20,
  },
  successCard: {
    marginBottom: theme.spacing.md,
    backgroundColor: `${theme.colors.success}05`,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.success,
  },
  successContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: theme.spacing.md,
  },
  successContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  successTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
    color: theme.colors.success,
    marginBottom: theme.spacing.xs,
  },
  successText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.success,
    lineHeight: 20,
  },
});
