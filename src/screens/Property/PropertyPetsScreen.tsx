import { StyleSheet, Text, View, ScrollView } from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Loading } from "../../components/Loading";
import { PropertyPetsRouteProp } from "../../navigation/types";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Pet } from "../../interfaces/pet";
import { getPetColor, getPetIcon } from "../../utils/convertions";
import { SimpleDetailTopCard } from "../../components/main/cards/SimpleDetailTopCard";
import { NoDataText } from "../../components/NoDataText";
import { DetailCardBase } from "../../components/main/cards/DetailCardBase";

export const PropertyPetsScreen: React.FC = () => {
  const route = useRoute<PropertyPetsRouteProp>();
  const { propertyId } = route.params || {};

  const { data: petsData, isLoading } = useCachedQuery<Pet[]>(
    `mobile/property/${propertyId}/pets`
  );

  const pets = petsData ?? [];

  if (isLoading) return <Loading />;

  return (
    <GradientView firstLineText="Mascotas">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Resumen */}
        <SimpleDetailTopCard
          icon="paw"
          label={pets.length === 1 ? "Mascota" : "Mascotas"}
          value={pets.length}
        />

        {/* Lista de mascotas */}
        {pets.length === 0 ? (
          <NoDataText text="No hay mascotas registradas" />
        ) : (
          pets.map((pet) => {
            const petIcon = getPetIcon(pet.type);
            const petColor = getPetColor(pet.type);

            return (
              <DetailCardBase border key={pet.id}>
                <Row align="center">
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: `${petColor}15` },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={petIcon}
                      size={32}
                      color={petColor}
                    />
                  </View>
                  <Col style={styles.petInfo}>
                    <Text style={styles.petName}>{pet.name}</Text>
                    <Row align="center" style={styles.detailsRow}>
                      <MaterialCommunityIcons
                        name="tag"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.petType}>{pet.type}</Text>
                    </Row>
                  </Col>
                  <View
                    style={[
                      styles.typeContainer,
                      { backgroundColor: petColor },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={petIcon}
                      size={20}
                      color={theme.colors.white}
                    />
                  </View>
                </Row>
              </DetailCardBase>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: theme.spacing.md,
    flexDirection: "column",
  },
  summaryRow: {
    padding: theme.spacing.md,
  },
  summaryTextContainer: {
    marginLeft: theme.spacing.md,
    alignItems: "center",
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xxl,
    fontWeight: "700",
    color: theme.colors.primary,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    fontWeight: "500",
  },
  noDataCard: {
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  petCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    flexDirection: "column",
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: "center",
    justifyContent: "center",
  },
  petInfo: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  petName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.xs,
  },
  detailsRow: {
    marginBottom: theme.spacing.xs,
  },
  petType: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
    textTransform: "capitalize",
  },
  typeContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
});
