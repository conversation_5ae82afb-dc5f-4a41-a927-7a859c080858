import { Text } from "react-native";
import { useState } from "react";
import { <PERSON>radientView } from "../../components/layouts/GradientView";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { ScrollView } from "react-native-gesture-handler";
import { PropertySelector } from "../../components/Property/PropertySelector";
import { ComplaintsSection } from "../../components/Sections/ComplaintsSection";
import { MaintenanceIssueReportsSection } from "../../components/Sections/MaintenanceIssueReportsSection";
import { PackageSection } from "../../components/Sections/PackageSection";

import { Loading } from "../../components/Loading";
import { PropertyInfoSection } from "../../components/Property/PropertyInfoSection";
import { FinesSection } from "../../components/Property/FinesSection";
import { InfractionsSection } from "../../components/Property/InfractionsSection";
import { ReservationsSection } from "../../components/Property/ReservationsSection";
import { PartialProperty } from "../../interfaces/property";
import { NoDataText } from "../../components/NoDataText";

export const PropertiesScreen: React.FC = () => {
  const {
    data: properties,
    isLoading,
    error,
  } = useCachedQuery<PartialProperty[]>(`mobile/property`);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(
    null
  );

  if (isLoading) return <Loading />;
  if (error) return <Text>Error al cargar los datos</Text>;
  if (!properties) return <Text>Sin datos</Text>;

  // Si no hay propiedades
  if (properties.length === 0) {
    return (
      <GradientView firstLineText="Mi propiedad">
        <ScrollView>
          <NoDataText text="No tienes propiedades registradas" />
        </ScrollView>
      </GradientView>
    );
  }

  // Determinar la propiedad seleccionada
  const selectedProperty =
    properties.length === 1
      ? properties[0]
      : properties.find((p) => p.id === selectedPropertyId) || properties[0];

  // Si hay múltiples propiedades pero no se ha seleccionado ninguna, usar la primera
  if (properties.length > 1 && !selectedPropertyId) {
    setSelectedPropertyId(properties[0].id);
  }

  return (
    <GradientView firstLineText="Mi propiedad">
      {/* Selector de propiedades para usuarios con múltiples propiedades */}
      <PropertySelector
        properties={properties}
        selectedPropertyId={selectedPropertyId}
        onPropertySelect={setSelectedPropertyId}
      />

      {/* Información básica de la propiedad */}
      <PropertyInfoSection property={selectedProperty} />

      {/* Reservaciones */}
      <ReservationsSection reservations={selectedProperty.reservations} />

      {/* Multas */}
      <FinesSection fines={selectedProperty.fines} />

      {/* Infracciones */}
      <InfractionsSection infractions={selectedProperty.infractions} />

      {/* Paquetes */}
      <PackageSection packages={selectedProperty.packages} />

      {/* Quejas */}
      <ComplaintsSection complaints={selectedProperty.complaints} />

      {/* Reportes de mantenimiento */}
      <MaintenanceIssueReportsSection
        maintenanceIssueReports={selectedProperty.maintenanceIssueReports}
      />
    </GradientView>
  );
};
