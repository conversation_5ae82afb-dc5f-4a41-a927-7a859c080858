import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Row } from "../../components/main/Row";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Loading } from "../../components/Loading";
import { PropertyParkingSpotsRouteProp } from "../../navigation/types";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { ParkingSpot, ParkingSpotType } from "../../interfaces/parking-spot";
import { NoDataText } from "../../components/NoDataText";
import { SimpleDetailTopCard } from "../../components/main/cards/SimpleDetailTopCard";

type ParkingSpotFilter = "ALL" | "AVAILABLE" | "OCCUPIED";

const getStatusColor = (isAvailable: boolean): string => {
  return isAvailable ? theme.colors.success : theme.colors.error;
};

const getStatusIcon = (
  isAvailable: boolean
): keyof typeof MaterialCommunityIcons.glyphMap => {
  return isAvailable ? "check-circle" : "close-circle";
};

const getStatusText = (isAvailable: boolean): string => {
  return isAvailable ? "Disponible" : "Ocupado";
};

const getTypeIcon = (
  type: ParkingSpotType
): keyof typeof MaterialCommunityIcons.glyphMap => {
  return type === ParkingSpotType.RESIDENT ? "home" : "account";
};

const getTypeLabel = (type: ParkingSpotType): string => {
  return type === ParkingSpotType.RESIDENT ? "Residente" : "Visitante";
};

export const PropertyParkingSpotsScreen: React.FC = () => {
  const route = useRoute<PropertyParkingSpotsRouteProp>();
  const { propertyId } = route.params || {};

  const { data: parkingSpotsData, isLoading } = useCachedQuery<ParkingSpot[]>(
    `mobile/property/${propertyId}/parking-spots`
  );

  const parkingSpots = parkingSpotsData ?? [];

  if (isLoading) return <Loading />;

  return (
    <GradientView firstLineText="Estacionamientos">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Resumen con filtros */}
        <SimpleDetailTopCard
          icon="car"
          label={
            parkingSpots.length === 1
              ? "Lugar de estacionamiento"
              : "Lugares de estacionamiento"
          }
          value={parkingSpots.length}
        />

        {/* Lista de espacios */}
        {parkingSpots.length === 0 ? (
          <NoDataText text="No hay espacios de estacionamiento registrados" />
        ) : (
          <View style={styles.spotsGrid}>
            {parkingSpots.map((spot) => (
              <TouchableOpacity
                key={spot.id}
                style={[
                  styles.spotCard,
                  {
                    backgroundColor: spot.isAvailable
                      ? `${theme.colors.success}15`
                      : `${theme.colors.error}15`,
                    borderColor: getStatusColor(spot.isAvailable),
                  },
                ]}
                activeOpacity={0.7}
                // onPress={() => {
                //   // Navegación a detalle de espacio si se implementa más adelante
                // }}
              >
                <Row align="center" style={styles.spotHeader}>
                  <MaterialCommunityIcons
                    name={getStatusIcon(spot.isAvailable)}
                    size={20}
                    color={getStatusColor(spot.isAvailable)}
                  />
                  <MaterialCommunityIcons
                    name={getTypeIcon(spot.type)}
                    size={16}
                    color={theme.colors.gray500}
                    style={styles.typeIcon}
                  />
                </Row>
                <Text style={styles.spotNumber}>{spot.spotNumber}</Text>
                <Text
                  style={[
                    styles.spotStatus,
                    { color: getStatusColor(spot.isAvailable) },
                  ]}
                >
                  {getStatusText(spot.isAvailable)}
                </Text>
                <Text style={styles.spotType}>{getTypeLabel(spot.type)}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: theme.spacing.md,
    flexDirection: "column",
  },
  summaryRow: {
    padding: theme.spacing.md,
    justifyContent: "space-around",
  },
  summaryItem: {
    alignItems: "center",
    padding: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    fontWeight: "500",
    textAlign: "center",
    marginTop: 2,
  },
  noDataCard: {
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    fontStyle: "italic",
  },
  spotsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: theme.spacing.xs,
  },
  spotCard: {
    width: "48%",
    aspectRatio: 1,
    borderRadius: theme.radii.lg,
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  spotHeader: {
    position: "absolute",
    top: theme.spacing.sm,
    left: theme.spacing.sm,
    right: theme.spacing.sm,
    justifyContent: "space-between",
  },
  typeIcon: {
    opacity: 0.7,
  },
  spotNumber: {
    fontSize: theme.fontSizes.xxl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.sm,
  },
  spotStatus: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    textAlign: "center",
    marginTop: theme.spacing.xs,
    textTransform: "uppercase",
  },
  spotType: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    fontWeight: "500",
    textAlign: "center",
    marginTop: 2,
  },
});
