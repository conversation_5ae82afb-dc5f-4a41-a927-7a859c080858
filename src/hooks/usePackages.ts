import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { Package, PackageDeliveryToken } from "../interfaces/package";

export const usePackages = () => {
  const queryClient = useQueryClient();

  // Obtener paquetes por propertyId
  const getPackagesByPropertyId = (propertyId: string) =>
    useQuery({
      queryKey: ["packages", propertyId],
      queryFn: async () => {
        const response = await hoaClient.get<Package[]>(
          `/mobile/property/${propertyId}/packages`
        );
        return response.data;
      },
      enabled: !!propertyId,
    });

  // Obtener un paquete por ID
  const getPackageById = (packageId: string) =>
    useQuery({
      queryKey: ["package", packageId],
      queryFn: async () => {
        const response = await hoaClient.get<Package>(
          `/mobile/package/${packageId}`
        );
        return response.data;
      },
      enabled: !!packageId,
    });

  // Generar token de entrega
  const generateDeliveryToken = useMutation({
    mutationFn: async (packageId: string) => {
      const response = await hoaClient.post<PackageDeliveryToken>(
        `/mobile/package/${packageId}/generate-token`
      );
      return response.data;
    },
    onSuccess: (_, packageId) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ["package", packageId] });
      queryClient.invalidateQueries({ queryKey: ["packages"] });
      queryClient.invalidateQueries({ queryKey: ["me"] });
    },
    onError: (error: any) => {
      console.error("Error generating delivery token:", error.response?.data);
    },
  });

  return {
    getPackagesByPropertyId,
    getPackageById,
    generateDeliveryToken,
  };
};
