import { useQuery } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { Facility } from "../interfaces/facility";
import { Reservation } from "../interfaces/reservation";

export const useFacilities = () =>
  useQuery({
    queryKey: ["facilities"],
    queryFn: async () => {
      const response = await hoaClient.get<Facility[]>(`/mobile/facilities`);
      return response.data;
    },
    staleTime: Infinity,
  });

// Hook para obtener facility básica sin reservaciones
export const useFacilityBasic = (facilityId: string) =>
  useQuery({
    queryKey: ["facility-basic", facilityId],
    queryFn: async () => {
      const response = await hoaClient.get<Omit<Facility, "reservations">>(
        `/mobile/facility/${facilityId}/basic`
      );
      return response.data;
    },
    enabled: !!facilityId,
    staleTime: 1000 * 60 * 10, // 10 minutos
  });

// Hook para obtener reservaciones de una facility por mes
export const useFacilityReservations = (
  facilityId: string,
  monthYear: string
) =>
  useQuery({
    queryKey: ["facility-reservations", facilityId, monthYear],
    queryFn: async () => {
      const response = await hoaClient.get<Reservation[]>(
        `/mobile/facility/${facilityId}/reservations`,
        {
          params: { month: monthYear },
        }
      );
      return response.data;
    },
    enabled: !!facilityId && !!monthYear,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });
