import { useQuery } from "@tanstack/react-query";
import { hoaClient } from "../api/axios-clients";
import { Facility } from "../interfaces/facility";

export const useFacilities = () =>
  useQuery({
    queryKey: ["facilities"],
    queryFn: async () => {
      const response = await hoaClient.get<Facility[]>(`/mobile/facilities`);
      return response.data;
    },
    staleTime: Infinity,
  });

export const useFacility = (facilityId: string) =>
  useQuery({
    queryKey: ["facility", facilityId],
    queryFn: async () => {
      const response = await hoaClient.get<Facility>(
        `/mobile/facility/${facilityId}`
      );
      return response.data;
    },
    enabled: !!facilityId,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });
