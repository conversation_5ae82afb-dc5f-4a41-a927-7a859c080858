import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Card, Col, Section } from "../main";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Row } from "../main/Row";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { NoDataSection } from "./NoDataSection";
import { getPackageStatusData } from "../../utils/convertions";
import { formatDateDMY } from "../../utils/date-time.utils";
import { PropertyPackage } from "../../interfaces/property";

interface PackageSectionProps {
  packages: PropertyPackage[];
}

export const PackageSection: React.FC<PackageSectionProps> = ({ packages }) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!packages.length) {
    return (
      <NoDataSection
        sectionTitle="Paquetes"
        text="No hay paquetes registrados"
      />
    );
  }

  return (
    <Section title="Paquetes">
      <>
        {/* Mostrar solo los primeros 3 paquetes pendientes */}
        {packages.slice(0, 3).map((pkg) => (
          <TouchableOpacity
            key={pkg.id}
            onPress={() =>
              navigation.navigate(PROPERTY_SCREENS.PACKAGE_DETAIL, {
                packageId: pkg.id,
              })
            }
          >
            <Card
              style={[
                styles.packageCard,
                { borderLeftColor: getPackageStatusData(pkg.status).color },
              ]}
            >
              <Col style={styles.packageInfo}>
                <Row style={styles.headerRow}>
                  <Text style={styles.notes} numberOfLines={2}>
                    {pkg.notes}
                  </Text>
                  <View
                    style={[
                      styles.statusChip,
                      {
                        backgroundColor: getPackageStatusData(pkg.status).color,
                      },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={getPackageStatusData(pkg.status).icon}
                      size={12}
                      color={theme.colors.white}
                    />
                    <Text style={styles.statusText}>
                      {getPackageStatusData(pkg.status).label}
                    </Text>
                  </View>
                </Row>
                <Row style={styles.dateRow}>
                  <MaterialCommunityIcons
                    name="package-variant"
                    size={14}
                    color={theme.colors.gray500}
                  />
                  <Text style={styles.dateText}>{pkg.number}</Text>
                </Row>
                <Row align="center" style={styles.dateRow}>
                  <MaterialCommunityIcons
                    name="calendar"
                    size={14}
                    color={theme.colors.gray500}
                  />
                  <Text style={styles.dateText}>
                    Recibido: {formatDateDMY(pkg.receivedAt)}
                  </Text>
                </Row>
                {pkg.deliveredAt && (
                  <Row align="center" style={styles.dateRow}>
                    <MaterialCommunityIcons
                      name="check-circle"
                      size={14}
                      color={theme.colors.success}
                    />
                    <Text style={styles.dateText}>
                      Entregado: {formatDateDMY(pkg.deliveredAt)}
                    </Text>
                  </Row>
                )}
              </Col>
            </Card>
          </TouchableOpacity>
        ))}
      </>
    </Section>
  );
};

const styles = StyleSheet.create({
  packageCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.radii.md,
    borderLeftWidth: 4,
  },
  headerRow: {
    alignItems: "flex-start",
  },
  notes: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
    lineHeight: 20,
  },
  packageInfo: {
    flex: 1,
    paddingLeft: theme.spacing.md,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  dateRow: {
    marginTop: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  packageNumber: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.xs,
  },
});
