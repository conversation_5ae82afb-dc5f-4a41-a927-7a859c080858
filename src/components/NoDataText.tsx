import { StyleSheet, Text } from "react-native";
import { theme } from "../theme";

interface NoDataTextProps {
  text: string;
}

export const NoDataText: React.FC<NoDataTextProps> = ({ text }) => {
  return <Text style={styles.noDataText}>{text}</Text>;
};

const styles = StyleSheet.create({
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
