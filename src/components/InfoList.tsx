import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { SmartAvatar } from "./main/Avatar/SmartAvatar";
import { theme } from "../theme";

interface InfoItem {
  title: string;
  subtitle?: string;
  onPress: () => void;
}

interface InfoListProps {
  items: InfoItem[];
}

export const InfoList: React.FC<InfoListProps> = ({ items }) => {
  return (
    <View style={styles.container}>
      {items.map((item, index) => (
        <TouchableOpacity
          key={index + item.title}
          style={styles.item}
          onPress={item.onPress}
        >
          <SmartAvatar name={item.title} />
          <View style={styles.textContainer}>
            <Text style={styles.title}>{item.title}</Text>
            {item.subtitle && (
              <Text style={styles.subtitle}>{item.subtitle}</Text>
            )}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  item: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: theme.spacing.md,
  },
  textContainer: {
    marginLeft: theme.spacing.md,
  },
  title: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "bold",
  },
  subtitle: {
    fontSize: 14,
    color: "gray",
  },
});
