import { StyleSheet, Text, View } from "react-native";
import { theme } from "../../../theme";
import { Col, Row } from "..";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { DetailCardBase } from "./DetailCardBase";

interface DetailTopCardProps {
  iconItem: {
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    color: string;
  };
  label: string;
  tags: { label: string; color: string }[];
}

export const DetailTopCard: React.FC<DetailTopCardProps> = ({
  iconItem,
  label,
  tags,
}) => {
  return (
    <DetailCardBase>
      <Row align="center" style={styles.headerRow}>
        <View
          style={[
            styles.statusIndicator,
            { backgroundColor: `${iconItem.color}20` },
          ]}
        >
          <MaterialCommunityIcons
            name={iconItem.icon}
            size={32}
            color={iconItem.color}
          />
        </View>
        <Col style={styles.headerInfo}>
          <Text style={styles.facilityName}>{label}</Text>
          <Row align="center" style={styles.chipRow}>
            {tags.map((tag, index) => (
              <View
                key={tag.label}
                style={
                  index === 0
                    ? [styles.statusChip, { backgroundColor: tag.color }]
                    : [
                        styles.statusChip,
                        { backgroundColor: tag.color },
                        styles.secondStatusChip,
                      ]
                }
              >
                <Text style={styles.statusText}>{tag.label}</Text>
              </View>
            ))}
          </Row>
        </Col>
      </Row>
    </DetailCardBase>
  );
};

const styles = StyleSheet.create({
  headerRow: {
    marginBottom: 0,
  },
  statusIndicator: {
    width: 64,
    height: 64,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
  },
  facilityName: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  chipRow: {
    gap: theme.spacing.sm,
  },
  statusChip: {
    alignSelf: "flex-start",
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.radii.md,
  },
  statusText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    color: theme.colors.white,
  },
  secondStatusChip: {
    marginLeft: theme.spacing.sm,
  },
});
