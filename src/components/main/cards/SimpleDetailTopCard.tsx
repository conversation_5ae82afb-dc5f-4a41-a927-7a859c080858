import { StyleSheet, Text } from "react-native";
import { theme } from "../../../theme";
import { Col, Row } from "..";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { DetailCardBase } from "./DetailCardBase";

interface SimpleDetailTopCardProps {
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  label: string;
  value: number;
}

export const SimpleDetailTopCard: React.FC<SimpleDetailTopCardProps> = ({
  icon,
  label,
  value,
}) => {
  return (
    <DetailCardBase noMargin>
      <Row>
        <Text style={styles.value}>{value}</Text>
        <Col justify="center" style={styles.column}>
          <MaterialCommunityIcons
            name={icon}
            size={24}
            color={theme.colors.primary}
          />
          <Text style={styles.label}>{label}</Text>
        </Col>
      </Row>
    </DetailCardBase>
  );
};

const styles = StyleSheet.create({
  column: {
    paddingLeft: theme.spacing.sm,
  },
  value: {
    fontSize: theme.fontSizes.xxxl,
    fontWeight: "700",
    color: theme.colors.primary,
  },
  label: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
  },
});
