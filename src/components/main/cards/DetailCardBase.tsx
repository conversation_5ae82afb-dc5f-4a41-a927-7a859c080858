import { View, StyleSheet } from "react-native";
import { theme } from "../../../theme/theme";

interface DetailTopCardBaseProps {
  children: React.ReactNode;
  border?: boolean;
  noMargin?: boolean;
}

export const DetailCardBase: React.FC<DetailTopCardBaseProps> = ({
  children,
  border,
  noMargin,
}) => {
  return (
    <View
      style={[
        styles.container,
        border && styles.borderedContainer,
        noMargin && styles.noMarginContainer,
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
  },
  noMarginContainer: {
    marginVertical: 0,
  },
  borderedContainer: {
    borderColor: theme.colors.primaryLight,
    borderWidth: 1,
    borderRadius: theme.radii.md,
  },
});
